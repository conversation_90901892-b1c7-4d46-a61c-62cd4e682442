["tests/test_box_detector.py::TestBoxDetector::test_box_detection_basic_flow", "tests/test_box_detector.py::TestBoxDetector::test_box_detection_no_reset", "tests/test_box_detector.py::TestBoxDetector::test_box_detection_with_offset", "tests/test_box_detector.py::TestBoxDetector::test_box_detection_zero_threshold", "tests/test_box_detector.py::TestBoxDetector::test_box_detector_creation", "tests/test_box_detector.py::TestBoxDetector::test_box_detector_creation_with_offset", "tests/test_box_detector.py::TestBoxDetector::test_context_manager", "tests/test_box_detector.py::TestBoxDetector::test_stop_detection", "tests/test_box_detector.py::TestBoxDetectorWithRecording::test_box_detector_with_recording_detects_exactly_2_boxes", "tests/test_box_detector.py::TestBoxDetectorWithRecording::test_box_detector_with_recording_no_offset", "tests/test_box_top.py::TestBoxTop::test_box_top_center_access", "tests/test_box_top.py::TestBoxTop::test_box_top_creation", "tests/test_box_top.py::TestBoxTop::test_box_top_extent_access", "tests/test_box_top.py::TestBoxTop::test_box_top_fields_comparison", "tests/test_box_top.py::TestBoxTop::test_box_top_rotation_access", "tests/test_box_top.py::TestBoxTop::test_box_top_string_representation", "tests/test_box_top.py::TestBoxTop::test_box_top_with_negative_coordinates", "tests/test_box_top.py::TestBoxTop::test_box_top_with_zero_volume", "tests/test_visualizer.py::TestNoOpVisualizer::test_noop_cleanup", "tests/test_visualizer.py::TestNoOpVisualizer::test_noop_context_manager", "tests/test_visualizer.py::TestNoOpVisualizer::test_noop_initialize", "tests/test_visualizer.py::TestNoOpVisualizer::test_noop_should_stop", "tests/test_visualizer.py::TestNoOpVisualizer::test_noop_update", "tests/test_visualizer.py::TestNoOpVisualizer::test_noop_visualizer_creation", "tests/test_visualizer.py::TestOpen3DVisualizer::test_cleanup_initialized", "tests/test_visualizer.py::TestOpen3DVisualizer::test_cleanup_not_initialized", "tests/test_visualizer.py::TestOpen3DVisualizer::test_cleanup_with_exception", "tests/test_visualizer.py::TestOpen3DVisualizer::test_context_manager_initialization_failure", "tests/test_visualizer.py::TestOpen3DVisualizer::test_context_manager_success", "tests/test_visualizer.py::TestOpen3DVisualizer::test_escape_key_callback", "tests/test_visualizer.py::TestOpen3DVisualizer::test_initial_state", "tests/test_visualizer.py::TestOpen3DVisualizer::test_initialize_already_initialized", "tests/test_visualizer.py::TestOpen3DVisualizer::test_initialize_failure", "tests/test_visualizer.py::TestOpen3DVisualizer::test_initialize_success", "tests/test_visualizer.py::TestOpen3DVisualizer::test_open3d_visualizer_creation", "tests/test_visualizer.py::TestOpen3DVisualizer::test_should_stop_initial", "tests/test_visualizer.py::TestOpen3DVisualizer::test_update_not_initialized", "tests/test_visualizer.py::TestOpen3DVisualizer::test_update_should_stop"]