from typing import Optional, Generator, Protocol

from box_top import BoxTop


class BoxTopDetectorProtocol(Protocol):
    """Protocol defining the interface for box top detector implementations."""

    def start_detection(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Start box top detection and yield BoxTop objects as a stream.

        Yields:
            BoxTop objects or None if detection fails
        """
        ...

    def stop_detection(self) -> None:
        """Stop the detection process."""
        ...

    def detect_single(self, roi_crop: bool = True) -> Optional[BoxTop]:
        """
        Detect a single box top.

        Args:
            roi_crop: Whether to apply ROI cropping

        Returns:
            BoxTop object or None if detection fails
        """
        ...


class BoxDetectorProtocol(Protocol):
    """Protocol defining the interface for box detector implementations."""

    def start_detection(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Start box detection and yield BoxTop objects as a stream.
        Only yields boxes when they cross the detection threshold.

        Yields:
            BoxTop objects or None if detection fails
        """
        ...

    def stop_detection(self) -> None:
        """Stop the detection process."""
        ...
