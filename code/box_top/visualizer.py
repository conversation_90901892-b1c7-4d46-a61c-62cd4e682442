from typing import Optional, Protocol
from contextlib import contextmanager
import numpy as np
import open3d as o3d
from loguru import logger


class VisualizerProtocol(Protocol):
    """Protocol defining the interface for visualizer implementations."""

    def initialize(self) -> bool:
        """
        Initialize the visualizer.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        ...

    def update(
        self,
        pcd: o3d.geometry.PointCloud,
        obb: o3d.geometry.OrientedBoundingBox,
        outlier_cloud: o3d.geometry.PointCloud,
    ) -> bool:
        """
        Update the visualizer with new geometry data.

        Args:
            pcd: Main point cloud
            obb: Oriented bounding box
            outlier_cloud: Outlier point cloud

        Returns:
            bool: True if update was successful, False if should stop
        """
        ...

    def cleanup(self) -> None:
        """Clean up the visualizer resources."""
        ...

    def should_stop(self) -> bool:
        """
        Check if the visualizer should stop (e.g., escape key pressed).

        Returns:
            bool: True if should stop, False otherwise
        """
        ...


class Open3DVisualizer:
    """
    Open3D-based visualizer implementation with proper escape key handling.
    """

    def __init__(self):
        """Initialize the Open3D visualizer."""
        self._visualizer: Optional[o3d.visualization.VisualizerWithKeyCallback] = None  # type: ignore
        self._pcd_geometry: Optional[o3d.geometry.PointCloud] = None
        self._obb_geometry: Optional[o3d.geometry.OrientedBoundingBox] = None
        self._outlier_geometry: Optional[o3d.geometry.PointCloud] = None
        self._should_stop = False
        self._initialized = False

    def _escape_key_callback(self, vis) -> bool:
        """Callback for escape key press."""
        logger.info("Escape key pressed, stopping visualization")
        self._should_stop = True
        return False

    def initialize(self) -> bool:
        """
        Initialize the visualizer window and settings.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        if self._initialized:
            return True

        try:
            self._visualizer = o3d.visualization.VisualizerWithKeyCallback()  # type: ignore
            self._visualizer.create_window(window_name="Box Top Detector Visualization")  # type: ignore

            # Register escape key callback (ESC key code is 256)
            self._visualizer.register_key_callback(256, self._escape_key_callback)  # type: ignore

            # Set rendering options for better visualization
            render_option = self._visualizer.get_render_option()  # type: ignore
            render_option.point_size = 2.0  # Make points more visible
            render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark background

            self._should_stop = False
            self._initialized = True
            logger.info("Visualizer initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize visualizer: {e}")
            return False

    def update(
        self,
        pcd: o3d.geometry.PointCloud,
        obb: o3d.geometry.OrientedBoundingBox,
        outlier_cloud: o3d.geometry.PointCloud,
    ) -> bool:
        """
        Update the visualizer with new geometry data.

        Args:
            pcd: Main point cloud
            obb: Oriented bounding box
            outlier_cloud: Outlier point cloud

        Returns:
            bool: True if update was successful, False if should stop
        """
        if not self._initialized or self._visualizer is None:
            logger.error("Visualizer not initialized")
            return False

        if self._should_stop:
            return False

        try:
            # First time setup - add geometries
            if self._pcd_geometry is None:
                self._pcd_geometry = pcd
                self._obb_geometry = obb
                self._outlier_geometry = outlier_cloud

                self._visualizer.add_geometry(self._pcd_geometry)
                self._visualizer.add_geometry(self._obb_geometry)
                self._visualizer.add_geometry(self._outlier_geometry)

                # Set initial viewpoint after adding geometries
                self._visualizer.get_view_control().set_front([-2.0, 4.0, -3.5])
                self._visualizer.get_view_control().set_lookat([0.0, 0.0, 1])
                self._visualizer.get_view_control().set_up([0.0, 0.0, -1.0])
                self._visualizer.get_view_control().set_zoom(2)
            else:
                # Update existing geometries in place
                self._pcd_geometry.points = pcd.points
                self._pcd_geometry.colors = pcd.colors
                if hasattr(pcd, "normals") and len(pcd.normals) > 0:
                    self._pcd_geometry.normals = pcd.normals

                assert self._outlier_geometry is not None
                assert self._obb_geometry is not None

                self._outlier_geometry.points = outlier_cloud.points
                self._outlier_geometry.colors = outlier_cloud.colors

                # Update OBB geometry
                self._obb_geometry.center = obb.center
                self._obb_geometry.extent = obb.extent
                self._obb_geometry.R = obb.R
                self._obb_geometry.color = obb.color

                # Update the visualizer with the modified geometries
                self._visualizer.update_geometry(self._pcd_geometry)
                self._visualizer.update_geometry(self._obb_geometry)
                self._visualizer.update_geometry(self._outlier_geometry)

            # Non-blocking update - this allows interaction
            self._visualizer.poll_events()
            self._visualizer.update_renderer()

            return not self._should_stop

        except Exception as e:
            logger.error(f"Error updating visualizer: {e}")
            return False

    def cleanup(self) -> None:
        """Clean up the visualizer resources."""
        if self._visualizer is not None:
            try:
                self._visualizer.destroy_window()
                logger.info("Visualizer cleaned up successfully")
            except Exception as e:
                logger.error(f"Error cleaning up visualizer: {e}")
            finally:
                self._visualizer = None
                self._pcd_geometry = None
                self._obb_geometry = None
                self._outlier_geometry = None
                self._initialized = False

    def should_stop(self) -> bool:
        """
        Check if the visualizer should stop (e.g., escape key pressed).

        Returns:
            bool: True if should stop, False otherwise
        """
        return self._should_stop

    def __enter__(self):
        """Enter the context manager and initialize visualizer."""
        if not self.initialize():
            raise RuntimeError("Failed to initialize visualizer")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager and cleanup visualizer."""
        self.cleanup()
        return False  # Don't suppress exceptions

    @contextmanager
    def visualization_context(self):
        """
        Context manager for visualization that ensures proper cleanup.

        Usage:
            with visualizer.visualization_context():
                # use visualizer
                pass
        """
        if not self.initialize():
            raise RuntimeError("Failed to initialize visualizer")
        try:
            yield self
        finally:
            self.cleanup()


class NoOpVisualizer:
    """
    No-operation visualizer that does nothing.
    Useful when visualization is disabled.
    """

    def initialize(self) -> bool:
        """Initialize (no-op)."""
        return True

    def update(
        self,
        pcd: o3d.geometry.PointCloud,
        obb: o3d.geometry.OrientedBoundingBox,
        outlier_cloud: o3d.geometry.PointCloud,
    ) -> bool:
        """Update (no-op)."""
        return True

    def cleanup(self) -> None:
        """Cleanup (no-op)."""
        pass

    def should_stop(self) -> bool:
        """Should stop (always False for no-op)."""
        return False

    def __enter__(self):
        """Enter the context manager."""
        self.initialize()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        self.cleanup()
        return False  # Don't suppress exceptions
