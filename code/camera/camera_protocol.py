from typing import Optional, Protocol

import open3d as o3d


class CameraProtocol(Protocol):
    """Protocol defining the interface for camera implementations."""

    def start_capture(self) -> bool:
        """
        Start the camera capture.

        Returns:
            bool: True if capture started successfully, False otherwise
        """
        ...

    def stop_capture(self) -> None:
        """Stop the camera capture."""
        ...

    def get_pointcloud(self) -> Optional[o3d.geometry.PointCloud]:
        """
        Get a point cloud from the camera.

        Returns:
            Open3D point cloud or None if failed
        """
        ...

    def get_device_info(self) -> dict:
        """
        Get information about the connected camera device.

        Returns:
            Dictionary containing device information
        """
        ...
