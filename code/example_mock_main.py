#!/usr/bin/env python3
"""
Example demonstrating how to run the different components using mocking.

This example shows how to use the box detection system with mock components
for testing, development, and demonstration purposes.
"""

from pprint import pprint
from loguru import logger

from box_top.box_top_detector_mock import BoxTopDetectorMock
from box_top.box_detector import BoxDetector
from box_top.visualizer import NoOpVisualizer, Open3DVisualizer


def example_1_basic_box_top_detection():
    """Example 1: Basic box top detection using mock data."""
    print("=" * 60)
    print("Example 1: Basic Box Top Detection with Mock Data")
    print("=" * 60)

    # Create a mock box top detector with recording data
    mock_box_top_detector = BoxTopDetectorMock("data/box_top_recording_01.txt", loop=False)

    print(f"Loaded {len(mock_box_top_detector._positions)} box top positions from recording")

    # Use context manager for proper cleanup
    try:
        count = 0
        for box_top in mock_box_top_detector.start_detection():
            if box_top is not None:
                count += 1
                print(f"\nBox Top #{count}:")
                print(
                    f"  Center: [{box_top.center[0]:.3f}, {box_top.center[1]:.3f}, {box_top.center[2]:.3f}]"
                )
                print(f"  Z Position: {box_top.z_position:.3f}")
                print(f"  Volume: {box_top.volume:.6f}")

                if count >= 3:  # Show only first 3 for brevity
                    print("  ... (showing first 3 only)")
                    break
    except KeyboardInterrupt:
        logger.info("Stopping detection...")
    finally:
        mock_box_top_detector.stop_detection()


def example_2_box_detection_with_filtering():
    """Example 2: Box detection with filtering logic."""
    print("\n" + "=" * 60)
    print("Example 2: Box Detection with Filtering Logic")
    print("=" * 60)

    # Create mock box top detector
    mock_box_top_detector = BoxTopDetectorMock("data/box_top_recording_01.txt", loop=False)

    # Create box detector with reset offset to prevent false positives
    box_detector = BoxDetector(mock_box_top_detector, reset_offset=0.1)

    print("Box detector configured with reset offset of 0.1")
    print("This means boxes must move to y < -0.1 before the next detection")

    detected_boxes = []

    # Use context manager for automatic cleanup
    with box_detector:
        for detected_box in box_detector.detect_boxes():
            if detected_box is not None:
                detected_boxes.append(detected_box)
                print(f"\n🎯 Box Detected #{len(detected_boxes)}:")
                print(f"   Center Y: {detected_box.center[1]:.3f}")
                print(f"   Volume: {detected_box.volume:.6f}")

    print(f"\nTotal boxes detected: {len(detected_boxes)}")


def example_4_complete_pipeline():
    """Example 4: Complete pipeline with all components."""
    print("\n" + "=" * 60)
    print("Example 4: Complete Pipeline with All Components")
    print("=" * 60)

    # Create all components
    mock_box_top_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=False)
    visualizer = NoOpVisualizer()  # Use NoOp for this example
    box_detector = BoxDetector(mock_box_top_detector, reset_offset=0.05)

    print("Pipeline components:")
    print("  📹 Mock Box Top Detector (from recording)")
    print("  🎯 Box Detector (with 0.05 reset offset)")
    print("  👁️  NoOp Visualizer (headless)")

    # Run the complete pipeline
    detected_boxes = []

    with visualizer, box_detector:
        print("\n🚀 Starting detection pipeline...")

        for box in box_detector.detect_boxes():
            if box is not None:
                detected_boxes.append(box)

                print(f"\n📦 Box #{len(detected_boxes)} detected:")
                print(
                    f"   Position: [{box.center[0]:.3f}, {box.center[1]:.3f}, {box.center[2]:.3f}]"
                )
                print(
                    f"   Extent: [{box.extent[0]:.3f}, {box.extent[1]:.3f}, {box.extent[2]:.3f}]"
                )
                print(
                    f"   Rotation: [{box.rotation[0]:.1f}°, {box.rotation[1]:.1f}°, {box.rotation[2]:.1f}°]"
                )
                print(f"   Volume: {box.volume:.6f} m³")

                # Simulate processing time
                import time

                time.sleep(0.1)

    print(f"\n✅ Pipeline completed. Total boxes detected: {len(detected_boxes)}")


def main():
    """Run all examples."""
    print("🎯 Box Detection System - Mock Examples")
    print("This demonstrates how to use the system with mock components")

    try:
        example_1_basic_box_top_detection()
        example_2_box_detection_with_filtering()
        example_4_complete_pipeline()

        print("\n" + "=" * 60)
        print("✅ All examples completed successfully!")
        print("=" * 60)

    except KeyboardInterrupt:
        print("\n\n⏹️  Examples interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error running examples: {e}")
        raise


if __name__ == "__main__":
    main()
