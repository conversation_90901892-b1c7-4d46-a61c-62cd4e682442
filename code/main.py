#!/usr/bin/env python3
"""
Main application demonstrating the box detection system.

This is a simple, clean example showing how all components work together
to detect boxes from a camera feed with optional visualization.
"""

import argparse
import sys
from loguru import logger

from camera.camera_realsense import RealSenseCamera
from box_top.box_top_detector import BoxTopDetector
from box_top.box_detector import BoxDetector
from box_top.visualizer import Open3DVisualizer, NoOpVisualizer


def create_camera():
    """Create and configure the camera."""
    camera = RealSenseCamera()
    return camera


def create_visualizer(enable_visualization: bool):
    """Create the appropriate visualizer based on user preference."""
    if enable_visualization:
        return Open3DVisualizer()
    else:
        return NoOpVisualizer()


def run_box_detection(enable_visualization: bool = True, reset_offset: float = 0.1):
    """
    Run the complete box detection pipeline.

    Args:
        enable_visualization: Whether to show the 3D visualization
        reset_offset: Reset offset for box detection to prevent false positives
    """
    logger.info("🚀 Starting Box Detection System")

    # Create components
    logger.info("📹 Initializing camera...")
    camera = create_camera()

    logger.info("👁️  Setting up visualizer...")
    visualizer = create_visualizer(enable_visualization)

    logger.info("🔍 Creating box top detector...")
    box_top_detector = BoxTopDetector(camera, visualizer=visualizer)

    logger.info("🎯 Creating box detector...")
    box_detector = BoxDetector(box_top_detector, reset_offset=reset_offset)

    # Run detection
    detected_count = 0

    try:
        logger.info("✨ Starting detection pipeline...")
        logger.info("Press Ctrl+C to stop, or ESC in visualization window")

        with box_detector:
            for detected_box in box_detector.detect_boxes():
                if detected_box is not None:
                    detected_count += 1

                    logger.info(
                        f"📦 Box #{detected_count} detected!"
                        f"   Position: [{detected_box.center[0] / 1000:.0f}, {detected_box.center[1] / 1000:.0f}, {detected_box.center[2] / 1000:.0f}]"
                        f"   Angle: {detected_box.rotation[0] % 90:.0f}°"
                        f"   Top size: {detected_box.extent[0] / 1000:.0f} x {detected_box.extent[1] / 1000:.0f} mm"
                    )

                    # Optional: Add processing logic here
                    # For example: send to robot, log to database, etc.

    except KeyboardInterrupt:
        logger.info("⏹️  Detection stopped by user")
    except Exception as e:
        logger.error(f"❌ Error during detection: {e}")
        raise
    finally:
        logger.info(f"✅ Detection completed. Total boxes detected: {detected_count}")


def run_mock_detection(reset_offset: float = 0.1):
    """
    Run box detection with mock data for testing/demo purposes.

    Args:
        reset_offset: Reset offset for box detection
    """
    logger.info("🎭 Starting Mock Box Detection System")

    from box_top.box_top_detector_mock import BoxTopDetectorMock

    # Create mock components
    logger.info("📁 Loading mock data...")
    mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=False)

    logger.info("🎯 Creating box detector...")
    box_detector = BoxDetector(mock_detector, reset_offset=reset_offset)

    # Run detection
    detected_count = 0

    try:
        logger.info("✨ Starting mock detection...")

        with box_detector:
            for detected_box in box_detector.detect_boxes():
                if detected_box is not None:
                    detected_count += 1

                    logger.info(f"📦 Box #{detected_count} detected!")
                    logger.info(
                        f"   Position: [{detected_box.center[0]:.3f}, {detected_box.center[1]:.3f}, {detected_box.center[2]:.3f}]"
                    )
                    logger.info(f"   Volume: {detected_box.volume:.6f} m³")

    except KeyboardInterrupt:
        logger.info("⏹️  Mock detection stopped by user")
    except Exception as e:
        logger.error(f"❌ Error during mock detection: {e}")
        raise
    finally:
        logger.info(f"✅ Mock detection completed. Total boxes detected: {detected_count}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Box Detection System - Detect boxes from camera or mock data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Run with camera and visualization
  python main.py --no-viz           # Run with camera, no visualization
  python main.py --mock             # Run with mock data
  python main.py --offset 0.2       # Run with custom reset offset
        """,
    )

    parser.add_argument("--mock", action="store_true", help="Use mock data instead of real camera")

    parser.add_argument(
        "--no-viz", action="store_true", help="Disable 3D visualization (headless mode)"
    )

    parser.add_argument(
        "--offset", type=float, default=0.1, help="Reset offset for box detection (default: 0.1)"
    )

    args = parser.parse_args()

    # Configure logging
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO",
    )

    try:
        if args.mock:
            run_mock_detection(reset_offset=args.offset)
        else:
            run_box_detection(enable_visualization=not args.no_viz, reset_offset=args.offset)

    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
