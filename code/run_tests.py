#!/usr/bin/env python3
"""Test runner for the box detection system using pytest."""

import subprocess
import sys
import os


def run_all_tests():
    """Run all unit tests using pytest."""
    cmd = ["python", "-m", "pytest", "tests/", "-v"]
    return subprocess.run(cmd, cwd=os.path.dirname(__file__)).returncode == 0


def run_tests_without_hardware():
    """Run tests excluding hardware-dependent tests."""
    cmd = ["python", "-m", "pytest", "tests/", "-v", "-m", "not hardware"]
    return subprocess.run(cmd, cwd=os.path.dirname(__file__)).returncode == 0


def run_hardware_tests():
    """Run only hardware-dependent tests."""
    cmd = ["python", "-m", "pytest", "tests/", "-v", "-m", "hardware"]
    return subprocess.run(cmd, cwd=os.path.dirname(__file__)).returncode == 0


def run_specific_test(test_file):
    """Run a specific test file."""
    cmd = ["python", "-m", "pytest", f"tests/{test_file}", "-v"]
    return subprocess.run(cmd, cwd=os.path.dirname(__file__)).returncode == 0


def main():
    """Main test runner function."""
    print("🧪 Box Detection System - Pytest Test Runner")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        
        if arg == "--no-hardware":
            print("Running tests without hardware dependencies...")
            success = run_tests_without_hardware()
        elif arg == "--hardware-only":
            print("Running only hardware-dependent tests...")
            success = run_hardware_tests()
        elif arg.startswith("test_"):
            print(f"Running specific test file: {arg}")
            success = run_specific_test(arg)
        else:
            print(f"Running specific test file: {arg}")
            success = run_specific_test(arg)
    else:
        print("Running all tests...")
        success = run_all_tests()
    
    print("=" * 50)
    if success:
        print("✅ All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
