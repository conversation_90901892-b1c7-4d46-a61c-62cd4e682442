"""Unit tests for BoxDetector class."""

import unittest
from unittest.mock import Mo<PERSON>, MagicMock
import numpy as np
from box_top import BoxTop
from box_top.box_detector import BoxDetector


class MockBoxTopDetector:
    """Mock box top detector for testing."""

    def __init__(self, box_tops):
        self.box_tops = box_tops
        self.index = 0
        self.stopped = False

    def start_detection(self):
        """Mock start detection that yields predefined box tops."""
        self.index = 0
        self.stopped = False
        while self.index < len(self.box_tops) and not self.stopped:
            yield self.box_tops[self.index]
            self.index += 1

    def stop_detection(self):
        """Mock stop detection."""
        self.stopped = True


class TestBoxDetector(unittest.TestCase):
    """Test cases for BoxDetector class."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test box tops with different center y values
        self.box_top_positive = BoxTop(
            center=np.array([0.0, 0.1, 0.8]),  # y = 0.1 (positive, should trigger)
            z_position=0.75,
            extent=np.array([0.2, 0.2, 0.1]),
            rotation=np.array([0.0, 0.0, 0.0]),
            volume=0.004,
        )

        self.box_top_negative = BoxTop(
            center=np.array([0.0, -0.2, 0.8]),  # y = -0.2 (negative, should reset)
            z_position=0.75,
            extent=np.array([0.2, 0.2, 0.1]),
            rotation=np.array([0.0, 0.0, 0.0]),
            volume=0.004,
        )

        self.box_top_zero = BoxTop(
            center=np.array([0.0, 0.0, 0.8]),  # y = 0.0 (should trigger)
            z_position=0.75,
            extent=np.array([0.2, 0.2, 0.1]),
            rotation=np.array([0.0, 0.0, 0.0]),
            volume=0.004,
        )

    def test_box_detector_creation(self):
        """Test BoxDetector creation with default parameters."""
        mock_detector = MockBoxTopDetector([])
        box_detector = BoxDetector(mock_detector)

        self.assertEqual(box_detector.reset_offset, 0.0)
        self.assertFalse(box_detector._should_stop)
        self.assertFalse(box_detector._waiting_for_reset)

    def test_box_detector_creation_with_offset(self):
        """Test BoxDetector creation with custom reset offset."""
        mock_detector = MockBoxTopDetector([])
        box_detector = BoxDetector(mock_detector, reset_offset=0.1)

        self.assertEqual(box_detector.reset_offset, 0.1)

    def test_box_detection_basic_flow(self):
        """Test basic box detection flow."""
        # Sequence: positive y (trigger) -> negative y (reset) -> positive y (trigger again)
        box_tops = [
            self.box_top_positive,  # Should be detected
            self.box_top_negative,  # Should reset state
            self.box_top_positive,  # Should be detected again
        ]

        mock_detector = MockBoxTopDetector(box_tops)
        box_detector = BoxDetector(mock_detector)

        detected_boxes = []
        for box in box_detector.detect_boxes():
            if box is not None:
                detected_boxes.append(box)

        # Should detect 2 boxes (first and third)
        self.assertEqual(len(detected_boxes), 2)
        self.assertEqual(detected_boxes[0], self.box_top_positive)
        self.assertEqual(detected_boxes[1], self.box_top_positive)

    def test_box_detection_with_offset(self):
        """Test box detection with reset offset."""
        # Create box top that's negative but not below offset
        box_top_small_negative = BoxTop(
            center=np.array([0.0, -0.05, 0.8]),  # y = -0.05
            z_position=0.75,
            extent=np.array([0.2, 0.2, 0.1]),
            rotation=np.array([0.0, 0.0, 0.0]),
            volume=0.004,
        )

        box_top_large_negative = BoxTop(
            center=np.array([0.0, -0.15, 0.8]),  # y = -0.15
            z_position=0.75,
            extent=np.array([0.2, 0.2, 0.1]),
            rotation=np.array([0.0, 0.0, 0.0]),
            volume=0.004,
        )

        box_tops = [
            self.box_top_positive,  # Should be detected
            box_top_small_negative,  # Should NOT reset (above -0.1 threshold)
            box_top_large_negative,  # Should reset (below -0.1 threshold)
            self.box_top_positive,  # Should be detected again
        ]

        mock_detector = MockBoxTopDetector(box_tops)
        box_detector = BoxDetector(mock_detector, reset_offset=0.1)

        detected_boxes = []
        for box in box_detector.detect_boxes():
            if box is not None:
                detected_boxes.append(box)

        # Should detect 2 boxes (first and fourth)
        self.assertEqual(len(detected_boxes), 2)

    def test_box_detection_zero_threshold(self):
        """Test detection at exactly zero threshold."""
        box_tops = [self.box_top_zero]  # y = 0.0, should trigger

        mock_detector = MockBoxTopDetector(box_tops)
        box_detector = BoxDetector(mock_detector)

        detected_boxes = []
        for box in box_detector.detect_boxes():
            if box is not None:
                detected_boxes.append(box)

        self.assertEqual(len(detected_boxes), 1)
        self.assertEqual(detected_boxes[0], self.box_top_zero)

    def test_box_detection_no_reset(self):
        """Test that without reset, only first positive detection triggers."""
        box_tops = [
            self.box_top_positive,  # Should be detected
            self.box_top_positive,  # Should NOT be detected (waiting for reset)
        ]

        mock_detector = MockBoxTopDetector(box_tops)
        box_detector = BoxDetector(mock_detector)

        detected_boxes = []
        for box in box_detector.detect_boxes():
            if box is not None:
                detected_boxes.append(box)

        # Should detect only 1 box
        self.assertEqual(len(detected_boxes), 1)

    def test_context_manager(self):
        """Test BoxDetector as context manager."""
        mock_detector = MockBoxTopDetector([self.box_top_positive])
        box_detector = BoxDetector(mock_detector)

        with box_detector:
            detected_boxes = []
            for box in box_detector.detect_boxes():
                if box is not None:
                    detected_boxes.append(box)
                    break  # Just test one detection

        # Should have called stop_detection automatically
        self.assertTrue(mock_detector.stopped)
        self.assertEqual(len(detected_boxes), 1)

    def test_stop_detection(self):
        """Test manual stop detection."""
        mock_detector = MockBoxTopDetector([self.box_top_positive])
        box_detector = BoxDetector(mock_detector)

        # Start detection in a separate context
        detection_gen = box_detector.detect_boxes()
        next(detection_gen)  # Get first result

        # Stop detection
        box_detector._stop_detection()

        # Should be stopped
        self.assertTrue(box_detector._should_stop)
        self.assertTrue(mock_detector.stopped)


class TestBoxDetectorWithRecording(unittest.TestCase):
    """Test BoxDetector with actual recording data."""

    def test_box_detector_with_recording_detects_exactly_2_boxes(self):
        """Test that BoxDetector detects exactly 2 boxes with the recording data."""
        from box_top.box_top_detector_mock import BoxTopDetectorMock

        # Use the actual recording file
        mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=False)
        box_detector = BoxDetector(mock_detector, reset_offset=0.1)

        detected_boxes = []

        # Use context manager for proper cleanup
        with box_detector:
            for box in box_detector.detect_boxes():
                if box is not None:
                    detected_boxes.append(box)

        # Should detect exactly 2 boxes based on the recording data
        self.assertEqual(
            len(detected_boxes), 2, f"Expected exactly 2 boxes, but detected {len(detected_boxes)}"
        )

        # Verify that both detected boxes have positive center y values
        for i, box in enumerate(detected_boxes):
            self.assertGreaterEqual(
                box.center[1],
                0.0,
                f"Box {i + 1} should have positive center y, got {box.center[1]}",
            )

        # Verify the boxes are different (not the same box detected twice)
        if len(detected_boxes) >= 2:
            # They should have different center positions
            box1_center = detected_boxes[0].center
            box2_center = detected_boxes[1].center

            # At least one coordinate should be different
            center_diff = np.abs(box1_center - box2_center)
            self.assertTrue(
                np.any(center_diff > 0.001),
                "The two detected boxes should have different center positions",
            )

    def test_box_detector_with_recording_no_offset(self):
        """Test BoxDetector with recording data and no reset offset."""
        from box_top.box_top_detector_mock import BoxTopDetectorMock

        # Use the actual recording file with no offset
        mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=False)
        box_detector = BoxDetector(mock_detector, reset_offset=0.0)

        detected_boxes = []

        with box_detector:
            for box in box_detector.detect_boxes():
                if box is not None:
                    detected_boxes.append(box)

        # Should still detect exactly 2 boxes even without offset
        self.assertEqual(
            len(detected_boxes),
            2,
            f"Expected exactly 2 boxes with no offset, but detected {len(detected_boxes)}",
        )


if __name__ == "__main__":
    unittest.main()
