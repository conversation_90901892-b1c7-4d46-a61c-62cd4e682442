"""Hardware-dependent tests that require actual RealSense camera."""

import pytest
from unittest.mock import patch
from camera.camera_realsense import RealSenseCamera
from box_top.box_top_detector import BoxTopDetector
from box_top.visualizer import NoOpVisualizer


@pytest.mark.hardware
class TestRealSenseCamera:
    """Test cases for RealSenseCamera that require actual hardware."""

    def test_camera_initialization_with_hardware(self):
        """Test RealSense camera initialization with actual hardware."""
        # This test requires actual RealSense hardware
        camera = RealSenseCamera()
        
        # Test that camera can be created (may fail if no hardware)
        assert camera is not None
        assert hasattr(camera, 'pipeline')
        assert hasattr(camera, 'config')

    def test_camera_start_capture_with_hardware(self):
        """Test starting camera capture with actual hardware."""
        camera = RealSenseCamera()
        
        try:
            # This will fail if no hardware is connected
            result = camera.start_capture()
            if result:  # Only test if hardware is available
                assert camera.get_pointcloud() is not None
                camera.stop_capture()
        except Exception:
            pytest.skip("RealSense hardware not available")

    def test_camera_context_manager_with_hardware(self):
        """Test camera context manager with actual hardware."""
        camera = RealSenseCamera()
        
        try:
            with camera:
                # If hardware is available, should work
                pcd = camera.get_pointcloud()
                assert pcd is not None
        except RuntimeError:
            pytest.skip("RealSense hardware not available")


@pytest.mark.hardware
class TestBoxTopDetectorWithHardware:
    """Test BoxTopDetector with actual hardware."""

    def test_box_top_detector_with_real_camera(self):
        """Test BoxTopDetector with real RealSense camera."""
        camera = RealSenseCamera()
        visualizer = NoOpVisualizer()
        
        try:
            detector = BoxTopDetector(camera, visualizer=visualizer)
            
            # Try to get a single detection
            box_top = detector.detect_single()
            
            # May be None if no box is visible, but should not crash
            assert box_top is None or hasattr(box_top, 'center')
            
        except RuntimeError:
            pytest.skip("RealSense hardware not available")

    def test_box_top_detector_streaming_with_hardware(self):
        """Test BoxTopDetector streaming with actual hardware."""
        camera = RealSenseCamera()
        visualizer = NoOpVisualizer()
        
        try:
            detector = BoxTopDetector(camera, visualizer=visualizer)
            
            # Test streaming for a short time
            count = 0
            with detector:
                for box_top in detector.start_detection():
                    count += 1
                    if count >= 5:  # Just test a few frames
                        break
            
            assert count > 0  # Should have processed some frames
            
        except RuntimeError:
            pytest.skip("RealSense hardware not available")


@pytest.mark.hardware
@pytest.mark.slow
class TestVisualizationWithHardware:
    """Test visualization with actual hardware (slow tests)."""

    def test_open3d_visualizer_with_real_data(self):
        """Test Open3D visualizer with real camera data."""
        from box_top.visualizer import Open3DVisualizer
        
        camera = RealSenseCamera()
        
        try:
            with camera:
                pcd = camera.get_pointcloud()
                if pcd is not None and len(pcd.points) > 0:
                    # Test that visualizer can handle real data
                    visualizer = Open3DVisualizer()
                    
                    # Don't actually show the window in tests
                    with patch.object(visualizer, '_visualizer') as mock_vis:
                        mock_vis.create_window.return_value = True
                        mock_vis.register_key_callback.return_value = True
                        mock_vis.get_render_option.return_value = type('MockRenderOption', (), {})()
                        
                        result = visualizer.initialize()
                        assert result
                        
                        # Test update with real point cloud data
                        import open3d as o3d
                        obb = o3d.geometry.OrientedBoundingBox()
                        outlier_cloud = o3d.geometry.PointCloud()
                        
                        result = visualizer.update(pcd, obb, outlier_cloud)
                        # Should work with real data
                        
                        visualizer.cleanup()
                
        except RuntimeError:
            pytest.skip("RealSense hardware not available")


# Helper function to check if hardware is available
def hardware_available():
    """Check if RealSense hardware is available."""
    try:
        camera = RealSenseCamera()
        result = camera.start_capture()
        if result:
            camera.stop_capture()
            return True
        return False
    except Exception:
        return False


# Skip all hardware tests if no hardware is available
pytestmark = pytest.mark.skipif(
    not hardware_available(),
    reason="RealSense hardware not available"
)
